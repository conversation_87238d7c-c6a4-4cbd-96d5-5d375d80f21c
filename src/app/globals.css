@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
  background: #000;
  color: #fff;
  min-height: 100vh;
  padding: 12px 0 0;
  /* desktop top padding only */
  overflow-x: hidden;
  touch-action: manipulation;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 16px;
}

/* Loading & Error overlays */
.loading-screen,
.error-screen {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  z-index: 1000;
}

.error-screen {
  z-index: 999;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid #333;
  border-top: 3px solid #00e0ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0)
  }

  100% {
    transform: rotate(360deg)
  }
}

.error-content {
  text-align: center;
  padding: 40px;
  color: #fff;
}

.error-content h2 {
  font-size: 24px;
  margin-bottom: 16px;
  color: #ff4444;
}

.error-content p {
  font-size: 16px;
  margin-bottom: 24px;
  color: #ccc;
}

.retry-button {
  background: #00e0ff;
  color: #0a0a0a;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
}

/* MAIN: hidden until ready */
.main-content {
  display: none;
}

.main-content.ready {
  /* shown after JS adds .ready */
  display: flex;
  justify-content: center;
  align-items: flex-start;
  /* centers the row horizontally, aligns to top vertically */
  min-height: calc(100vh - 12px);
  width: 100%;
  padding-top: 20px;
}

/* Row that contains video + metadata (centered) */
.row {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 8px;
  /* minimal visible space between cards */
  max-width: 100%;
}

/* Video Card (desktop): near full height, portrait */
.video-card {
  position: relative;
  background: #000;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(29, 29, 29, 0.4);

  height: calc(100vh - 24px);
  max-height: 980px;
  aspect-ratio: 9 / 16;
  width: auto;
}

.main-video {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #000;
  /* ensure black, not gray */
  outline: none;
}

/* Metadata Card (desktop minimal) */
.metadata-card {
  background: #141414;
  border: 1px solid #2b2b2b;
  border-radius: 20px;
  padding: 14px 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.35);
  height: max-content;
  width: 340px;
}

.video-metadata h1 {
  font-family: 'Iceland', cursive;
  font-size: 24px;
  letter-spacing: .5px;
  margin-bottom: 8px;
  font-weight: 400;
}

.video-metadata p {
  font-size: 13px;
  line-height: 1.5;
  color: #cfcfcf;
  margin-bottom: 12px;
}

/* Buttons (gradients) */
.btn {
  border: none;
  padding: 10px 16px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 700;
  /* Use heavier bold weight */
  cursor: pointer;
  width: 100%;
  color: #fff;
  transition: transform .15s ease, filter .15s ease;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  /* Ensure consistent font family */
}


.btn:hover {
  transform: translateY(-1px);
  filter: brightness(1.05);
}

/* Primary: cyan on RIGHT, blue left */
.btn-primary {
  background: linear-gradient(90deg, #ff3db8 0%, #00e0ff 100%);
}

.signup-section {
  background: #1b1b1b;
  padding: 12px;
  border-radius: 12px;
  border: 1px solid #2a2a2a;
  text-align: center;
}

.signup-section h3 {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 6px;
}

.signup-section p {
  font-size: 12px;
  color: #bdbdbd;
  margin-bottom: 10px;
}

/* Controls */
.video-controls {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.play-pause-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all .3s ease;
  color: #fff;
  pointer-events: all;
  opacity: 0;
  z-index: 30;
}

.play-pause-btn.show {
  opacity: 1;
}

.play-pause-btn:hover {
  background: rgba(0, 0, 0, 0.85);
  transform: translate(-50%, -50%) scale(1.04);
}

.play-pause-btn svg {
  width: 28px;
  height: 28px;
}

.mute-btn {
  position: absolute;
  bottom: 45px;
  right: 15px;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all .3s ease;
  color: #fff;
  pointer-events: all;
  z-index: 30;
}

.mute-btn:hover {
  background: rgba(0, 0, 0, 0.85);
  transform: scale(1.04);
}

.mute-btn svg {
  width: 18px;
  height: 18px;
}

.progress-container {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  z-index: 20;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: #fff;
  width: 0%;
  transition: width .1s ease;
}

/* Mobile signup button */
.mobile-signup {
  position: absolute;
  bottom: 56px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 25;
  display: none;
}

.mobile-signup .btn {
  width: auto;
  min-width: 120px;
}

/* Hide controls toggler */
.video-card.hide-controls .play-pause-btn {
  opacity: 0;
  pointer-events: none;
}

/* iOS inline */
@supports (-webkit-touch-callout: none) {
  .main-video {
    -webkit-playsinline: true;
  }
}

/* ===== Responsive ===== */
@media (max-width: 768px) {

  /* No padding anywhere (fixes stray bottom padding/scroll) */
  body {
    padding: 0;
  }

  .container {
    padding: 0;
  }

  .main-content.ready {
    display: block;
    /* simple flow on mobile */
    min-height: auto;
  }

  .row {
    display: block;
  }

  .video-card {
    width: 100vw;
    /* full width */
    height: calc(100vw * 16 / 9);
    /* maintain 9:16 */
    max-width: none;
    margin: 0 auto;
    border-radius: 20px;
  }

  .metadata-card {
    display: none;
  }

  /* hidden on mobile */
  .mobile-signup {
    display: block;
  }
}

@media (max-width: 480px) {
  .play-pause-btn {
    width: 60px;
    height: 60px;
  }

  .play-pause-btn svg {
    width: 24px;
    height: 24px;
  }

  .mute-btn {
    width: 34px;
    height: 34px;
    bottom: 34px;
    right: 12px;
  }

  .mute-btn svg {
    width: 16px;
    height: 16px;
  }
}